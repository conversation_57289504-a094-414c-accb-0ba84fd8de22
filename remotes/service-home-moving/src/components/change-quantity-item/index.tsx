import React, { useEffect, useMemo, useState } from 'react';
import { Pressable, TextStyle, ViewStyle } from 'react-native';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  HitSlop,
  IconAssets,
  IconImage,
  IconImageProps,
} from '@btaskee/design-system';

import { styles } from './styles';

export type ChangeQuantityItemProps = {
  testIDs?: {
    value?: string;
    minus?: string;
    plus?: string;
  };
  title?: string;
  value?: number;
  unit?: string;
  min?: number;
  max?: number;
  step?: number;
  titleTextTransform?: TextStyle['textTransform'];
  onChange?: (value: number) => void;
};

type ChangeButtonProps = {
  testID?: string;
  sign: IconImageProps['source'];
  disabled?: boolean;
  onPress?: () => void;
};

const ChangeButton = ({
  testID,
  sign,
  disabled,
  onPress,
}: ChangeButtonProps) => {
  const styleDisabled: ViewStyle = useMemo(() => {
    return {
      backgroundColor: disabled
        ? ColorsV2.neutralDisable
        : ColorsV2.neutralWhite,
    };
  }, [disabled]);

  return (
    <Pressable
      testID={testID}
      hitSlop={HitSlop.MEDIUM}
      style={[styles.changeButtonContainer, styleDisabled]}
      onPress={onPress}
      disabled={disabled}
    >
      <IconImage
        source={sign}
        size={20}
        color={disabled ? ColorsV2.neutralWhite : ColorsV2.neutral800}
      />
    </Pressable>
  );
};

export const ChangeQuantityItem = ({
  testIDs,
  title,
  value = 0,
  unit,
  max,
  min,
  step = 1,
  titleTextTransform = 'uppercase',
  onChange,
}: ChangeQuantityItemProps) => {
  const [currentValue, setCurrentValue] = useState(value);

  const unitTxt = ` ${unit}`;

  const disabledPlus = useMemo(() => {
    if (typeof max === 'number') {
      return currentValue >= max;
    }
    return false;
  }, [max, currentValue]);

  const disabledMinus = useMemo(() => {
    if (typeof min === 'number') {
      return currentValue <= min;
    }
    return false;
  }, [min, currentValue]);

  useEffect(() => {
    setCurrentValue(value);
  }, [value]);

  const onPlus = () => {
    const newValue = currentValue + step;
    _onChange(newValue);
  };

  const onMinus = () => {
    const newValue = currentValue - step;
    _onChange(newValue);
  };

  const _onChange = (newValue: number) => {
    setCurrentValue(newValue);
    onChange?.(newValue);
  };

  return (
    <BlockView style={styles.container}>
      <ConditionView
        condition={Boolean(title)}
        viewTrue={
          <CText
            margin={{ bottom: 8 }}
            style={{ textTransform: titleTextTransform }}
          >
            {title}
          </CText>
        }
      />
      <BlockView style={styles.contentContainer}>
        <ChangeButton
          testID={testIDs?.minus}
          sign={IconAssets.icSubtractFill}
          onPress={onMinus}
          disabled={disabledMinus}
        />
        <CText
          testID={testIDs?.value}
          flex
          center
          bold
          size={20}
        >
          {currentValue}
          <ConditionView
            condition={Boolean(unit)}
            viewTrue={
              <CText
                center
                size={16}
              >
                {unitTxt}
              </CText>
            }
          />
        </CText>
        <ChangeButton
          testID={testIDs?.plus}
          sign={IconAssets.icPlusFill}
          onPress={onPlus}
          disabled={disabledPlus}
        />
      </BlockView>
    </BlockView>
  );
};
