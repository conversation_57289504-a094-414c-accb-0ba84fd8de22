import React, { useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DateTimeHelpers,
  getTextWithLocale,
  HomeMovingProgressPostTaskType,
  IDate,
  IHomeDetail,
  IHomeTypeHomeMoving,
  IUserLocation,
  Maybe,
  NameHomeTypeHomeMoving,
  NameOptionByHomeTypeHomeMoving,
  NameOptionTypeFurnitureHomeMoving,
  Spacing,
  ToastHelpers,
  useAppStore,
  useUserStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import {
  ConfirmAddInBuildingTypeContent,
  ConfirmChangeHomeTypeContent,
} from '@components';
import {
  icCart,
  icElevatorTransport,
  icGarage,
  icStair,
  imgHouseMoving,
  imgServicedApartment,
} from '@images';
import { RouteName } from '@navigation/RouteName';

import { useAppNavigation } from './useAppNavigation';
import { useI18n } from './useI18n';

export const usePostTaskHomeMoving = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { locale } = useAppStore();
  const { user } = useUserStore();
  const {
    oldHomeDetail,
    newHomeDetail,
    passStep,
    currentStep,
    locations,
    isCanMoveInBuilding,
    isInBuilding,
    price,
    service,
    setAddress,
    setTemptStairsTransportStep1,
    setHomeNumber,
    setHomeDetail,
    setIsCanMoveInBuilding,
    setPassStep,
    setCurrentStep,
    setIsInBuilding,
  } = usePostTaskStore();

  const timezonesOld = DateTimeHelpers.getTimezoneByCity(
    oldHomeDetail?.addressDetail?.taskPlace?.city,
  );

  const getIsShowAddIsInBuilding = useCallback(() => {
    // Nếu ở step 2 (Chọn nơi đến) và loại nhà của nơi đi có option isInBuilding thì show
    return Boolean(
      currentStep === HomeMovingProgressPostTaskType.Step2 &&
        isCanMoveInBuilding,
    );
  }, [currentStep, isCanMoveInBuilding]);

  const getDataPostTaskMoving = useCallback(
    (step: HomeMovingProgressPostTaskType) => {
      let homeDetail: IHomeDetail = {};
      switch (step) {
        case HomeMovingProgressPostTaskType.Step1:
          homeDetail = oldHomeDetail;
          break;
        case HomeMovingProgressPostTaskType.Step2:
          homeDetail = newHomeDetail;
          break;

        default:
          break;
      }

      return {
        homeDetail,
      };
    },
    [newHomeDetail, oldHomeDetail],
  );

  const getDetailSettingHomeMoving = useCallback(
    (city?: string) => {
      const detail = service?.detailService?.homeMoving?.city?.find(
        (e) => e.name === city,
      );
      return detail;
    },
    [service],
  );
  const checkIsSupportCity = useCallback(
    (city?: string) => {
      return Boolean(getDetailSettingHomeMoving(city));
    },
    [getDetailSettingHomeMoving],
  );

  const getDetailSettingHomeType = useCallback(
    ({
      city,
      nameHomeType,
    }: {
      city?: string;
      nameHomeType?: NameHomeTypeHomeMoving;
    }) => {
      const detailSettingHomeMoving = getDetailSettingHomeMoving(city);
      return detailSettingHomeMoving?.homeType?.find(
        (e) => e.name === nameHomeType,
      );
    },
    [getDetailSettingHomeMoving],
  );

  const setAddressPostTask = useCallback((location?: Maybe<IUserLocation>) => {
    setAddress({
      lat: location?.lat,
      lng: location?.lng,
      country: location?.country,
      city: location?.city,
      district: location?.district,
      address: location?.address,
      contact: location?.contact,
      phoneNumber: location?.phoneNumber,
      shortAddress: location?.shortAddress,
      countryCode: location?.countryCode,
      isAddressMaybeWrong: Boolean(location?.isAddressMaybeWrong),
      homeType: location?.homeType,
      description: location?.description,
    });
    setHomeNumber(location?.description);
  }, []);

  const onChangeLocation = useCallback(
    ({
      step,
      location,
    }: {
      step: HomeMovingProgressPostTaskType;
      location: Maybe<IUserLocation>;
    }) => {
      const homeDetail: IHomeDetail = {
        addressDetail: {
          address: location?.address,
          lat: location?.lat,
          lng: location?.lng,
          contactName: location?.contact,
          description: location?.description,
          phone: location?.phoneNumber,
          shortAddress: location?.shortAddress,
          taskPlace: {
            country: location?.country,
            city: location?.city,
            district: location?.district,
            isAddressMaybeWrong: Boolean(location?.isAddressMaybeWrong),
          },
        },
      };
      if (step === HomeMovingProgressPostTaskType.Step1) {
        setAddressPostTask(location);
      }
      setHomeDetail({
        step,
        homeDetail,
      });
    },
    [],
  );

  const onChooseHomeType = ({
    step,
    homeType,
  }: {
    step: HomeMovingProgressPostTaskType;
    homeType: IHomeTypeHomeMoving;
  }) => {
    const isStepCurrentLocation = step === HomeMovingProgressPostTaskType.Step1;
    const onChange = () => {
      const homeDetail = getDataPostTaskMoving(step).homeDetail;

      //Nếu thay đổi loại nhà nơi đến thì clear option clean
      const optionClean =
        step === HomeMovingProgressPostTaskType.Step2
          ? { date: null, isCleaningRequired: false, taskNote: '' }
          : { ...homeDetail };

      if (isStepCurrentLocation) {
        setIsCanMoveInBuilding(Boolean(homeType.isCanMoveInBuilding));
      }

      // trackingServiceClick({
      //   screenName:
      //     step === HomeMovingProgressPostTaskType.Step1
      //       ? TrackingScreenNames.CurrentLocation
      //       : TrackingScreenNames.NewLocation,
      //   serviceName: SERVICES?.HOME_MOVING,
      //   action: TRACKING_ACTION.ChooseHouseType,
      //   additionalInfo: {
      //     newHomeType: homeDetail?.homeType?.name,
      //   },
      // });

      setHomeDetail({
        step,
        homeDetail: {
          ...homeDetail,
          ...optionClean,
          homeType: {
            name: homeType.name,
            text: homeType.text,
          },
          options: [],
        },
      });
    };

    if (isStepCurrentLocation && isInBuilding) {
      Alert.alert.open({
        title: t('CONFIRM_CHANGE'),
        titleStyle: { fontSize: Spacing.SPACE_20 },
        message: (
          <ConfirmChangeHomeTypeContent
            onConfirm={() => {
              setIsInBuilding(false);
              setHomeDetail({
                step: HomeMovingProgressPostTaskType.Step2,
                homeDetail: {},
              });
              onChange();
            }}
          />
        ),
      });
      return;
    }

    onChange();
  };

  const getContentByHomeType = useCallback(
    (nameHomeTypeMoving?: NameHomeTypeHomeMoving) => {
      let labelArea = '';
      let subLabelArea = '';
      switch (nameHomeTypeMoving) {
        case NameHomeTypeHomeMoving.apartment:
          labelArea = t('SELECT_AREA_APARTMENT');
          subLabelArea = t('SUB_SELECT_AREA_APARTMENT');
          break;

        case NameHomeTypeHomeMoving.house:
          labelArea = t('SELECT_AREA_HOUSE');
          subLabelArea = t('SUB_SELECT_AREA_HOUSE');
          break;

        default:
          break;
      }
      return {
        labelArea,
        subLabelArea,
      };
    },
    [t],
  );

  const onPressStep = useCallback(
    (step: HomeMovingProgressPostTaskType) => {
      const dataPostTask = getDataPostTaskMoving(currentStep);
      const isSupportCity = checkIsSupportCity(
        dataPostTask?.homeDetail?.addressDetail?.taskPlace?.city,
      );
      const nameHomeTypeMoving = dataPostTask.homeDetail.homeType?.name;

      const isStepChooseHomeDetail = [
        HomeMovingProgressPostTaskType.Step1,
        HomeMovingProgressPostTaskType.Step2,
      ].includes(currentStep);

      // Nếu ở step con địa chi và chưa chọn diện tích thì show lỗi
      if (
        isStepChooseHomeDetail &&
        passStep >= HomeMovingProgressPostTaskType.Step2
      ) {
        if (!isSupportCity) {
          //Địa chỉ không được hỗ trợ
          ToastHelpers.showWarning({ message: t('PLS_CHOOSE_CITY') });
          return;
        } else if (!nameHomeTypeMoving) {
          //Chưa chọn loại nhà
          ToastHelpers.showWarning({
            message: t('PLS_CHOOSE_HOME_TYPE'),
          });
          return;
        } else {
          if (
            currentStep === HomeMovingProgressPostTaskType.Step1 &&
            !dataPostTask.homeDetail?.homeType?.type?.name
          ) {
            //Nếu đang ở step 1 và chưa chọn diện tích
            const message =
              getContentByHomeType(nameHomeTypeMoving).subLabelArea;
            ToastHelpers.showWarning({ message });
            return;
          }
        }
      }
      // trackingHomeMovingProgress(step);
      setCurrentStep(step);
    },
    [
      checkIsSupportCity,
      currentStep,
      getContentByHomeType,
      getDataPostTaskMoving,
      passStep,
      setCurrentStep,
      t,
    ],
  );

  const onNextStep = useCallback(
    (step: HomeMovingProgressPostTaskType) => {
      // trackingStepHomeMoving({ step, action: TRACKING_ACTION.Next });
      if (step < HomeMovingProgressPostTaskType.Step5) {
        const newStep = step + 1;
        onPressStep(newStep);
        if (newStep > passStep) {
          setPassStep(newStep);
        }
      } else {
        navigation.navigate(RouteName.ConfirmAndPayment);
      }
    },
    [navigation, onPressStep, passStep],
  );

  const getContentByStep = useCallback(
    (step: HomeMovingProgressPostTaskType) => {
      let title = '';
      let labelButton = '';
      let labelOptionClean = '';
      let descriptionOptionClean = '';
      switch (step) {
        case HomeMovingProgressPostTaskType.Step1:
          title = t('PLACE_TO_MOVE');
          labelButton = t('SELECT_PLACE_TO_MOVE_TO');
          labelOptionClean = t('PLACE_TO_MOVE_CLEANING');
          descriptionOptionClean = t('SUB_PLACE_TO_MOVE_CLEANING');
          break;

        case HomeMovingProgressPostTaskType.Step2:
          title = t('PLACE_TO_MOVE_TO');
          labelButton = t('SELECT_ITEMS_TO_MOVE');
          labelOptionClean = t('PLACE_TO_MOVE_TO_CLEANING');
          descriptionOptionClean = t('SUB_PLACE_TO_MOVE_TO_CLEANING');
          break;

        case HomeMovingProgressPostTaskType.Step3:
          title = t('SELECT_ITEMS_TO_MOVE');
          labelButton = t('SELECT_TIME');
          break;

        case HomeMovingProgressPostTaskType.Step4:
          title = t('SELECT_TIME');
          break;

        case HomeMovingProgressPostTaskType.Step5:
          title = t('OVERVIEW');
          break;

        default:
          break;
      }
      return {
        title,
        labelButton,
        labelOptionClean,
        descriptionOptionClean,
      };
    },
    [t],
  );

  const getBlackListLocation = useCallback(() => {
    const address = [
      oldHomeDetail?.addressDetail?.address,
      newHomeDetail?.addressDetail?.address,
    ];
    return {
      address,
    };
  }, [
    newHomeDetail?.addressDetail?.address,
    oldHomeDetail?.addressDetail?.address,
  ]);

  const getIconOptionHomeType = useCallback(
    (nameOptionHomeType: NameOptionByHomeTypeHomeMoving) => {
      switch (nameOptionHomeType) {
        case NameOptionByHomeTypeHomeMoving.stairsTransport:
          return icStair;

        case NameOptionByHomeTypeHomeMoving.garage:
          return icGarage;

        case NameOptionByHomeTypeHomeMoving.byroad:
          return icCart;

        case NameOptionByHomeTypeHomeMoving.elevatorTransport:
          return icElevatorTransport;

        default:
          break;
      }
    },
    [],
  );

  const getSubTitleOptionTypeFurniture = useCallback(
    (nameOption?: NameOptionTypeFurnitureHomeMoving) => {
      let subTitle = '';
      switch (nameOption) {
        case NameOptionTypeFurnitureHomeMoving.airConditioner:
          subTitle = t('OPTION_FURNITURE_AIR_CONDITIONER');
          break;

        case NameOptionTypeFurnitureHomeMoving.waterHeater:
          subTitle = t('OPTION_FURNITURE_WATER_HEATER');
          break;

        default:
          break;
      }
      return subTitle;
    },
    [t],
  );

  const getSettingBookTask = useCallback(() => {
    const settingTimeOldHomeCleaning =
      oldHomeDetail?.homeType?.type?.oldHomeCleaning;
    const settingTimeNewHomeCleaning =
      newHomeDetail?.homeType?.type?.newHomeCleaning;
    return {
      oldHome: settingTimeOldHomeCleaning,
      newHome: settingTimeNewHomeCleaning,
    };
  }, [oldHomeDetail?.homeType?.type, newHomeDetail?.homeType?.type]);

  const getIsPriceIncrease = useCallback(() => {
    const isPriceIncrease =
      price?.subTasksCostDetail?.oldHomeCleaningCostDetail?.isIncrease ||
      price?.subTasksCostDetail?.newHomeCleaningCostDetail?.isIncrease;
    const isPriceIncreaseMoving =
      price?.subTasksCostDetail?.movingCostDetail?.isIncrease;
    return {
      cleaningHome: isPriceIncrease,
      moving: isPriceIncreaseMoving,
    };
  }, [price]);

  const getAcreageText = useCallback(
    (data: Maybe<IHomeDetail>) => {
      return (
        (data?.homeType?.type?.option?.text
          ? `${getTextWithLocale(
              data?.homeType?.type?.text,
              locale,
            )} ${getTextWithLocale(data?.homeType?.type?.option?.text, locale)}`
          : getTextWithLocale(data?.homeType?.type?.text, locale)) || ''
      );
    },
    [locale],
  );

  const getImageHomeType = useCallback(
    (nameHomeType?: NameHomeTypeHomeMoving) => {
      let sourceImage = null;
      switch (nameHomeType) {
        case NameHomeTypeHomeMoving.house:
          sourceImage = imgHouseMoving;
          break;
        case NameHomeTypeHomeMoving.apartment:
          sourceImage = imgServicedApartment;
          break;

        default:
          break;
      }
      return sourceImage;
    },
    [],
  );

  const getContentOptionHomeType = useCallback(
    (nameOption?: NameOptionByHomeTypeHomeMoving) => {
      let title = '';
      let note = '';
      switch (nameOption) {
        case NameOptionByHomeTypeHomeMoving.stairsTransport:
          title = t('SUB_TITLE_OPTION_STAIRS_TRANSPORT');
          note = t('NOTE_OPTION_STAIRS_TRANSPORT');
          break;

        case NameOptionByHomeTypeHomeMoving.byroad:
          title = t('SUB_TITLE_OPTION_BYROAD');
          note = t('NOTE_OPTION_BYROAD');
          break;

        default:
          break;
      }
      return {
        title,
        note,
      };
    },
    [t],
  );

  const getBookTaskAfterHours = useCallback(() => {
    const settingBookTask = getSettingBookTask();
    return {
      old: settingBookTask?.oldHome?.bookTaskAfterHours || 1,
      new: settingBookTask?.newHome?.bookTaskAfterHours || 3,
    };
  }, [getSettingBookTask]);

  const getDateCleaningDefault = useCallback(
    (dateMoving: IDate) => {
      const bookTaskAfterHours = getBookTaskAfterHours();
      return {
        old: DateTimeHelpers.toDateTz({
          timezone: timezonesOld,
          date: dateMoving,
        }).add(bookTaskAfterHours.old, 'hour'),
        new: DateTimeHelpers.toDateTz({
          timezone: timezonesOld,
          date: dateMoving,
        }).add(bookTaskAfterHours.new, 'hour'),
      };
    },
    [getBookTaskAfterHours, timezonesOld],
  );

  const onAddLocation = useCallback(
    ({
      title,
      step,
      isShowAddIsInBuilding,
    }: {
      title?: string;
      step: HomeMovingProgressPostTaskType;
      isShowAddIsInBuilding?: boolean;
    }) => {
      Alert.alert.open({
        title: 'Thông báo',
        message: 'Chức năng đang được phát triển',
        actions: [{ text: 'Đóng' }],
      });
      // navigation.navigate(RouteName.Map, {
      //   title,
      //   isShowAddIsInBuilding,
      //   callback: (location) => {
      //     addLocation(location);
      //     onChangeLocation?.({ step, location });
      //   },
      // });
    },
    [],
  );

  const getLocations = useCallback(
    (blacklistAddress?: Maybe<string>[]) => {
      const locationsMoving = user?.homeMovingLocations || [];

      //Filter những location chưa có trong locationsMoving
      const locationsTempt = locations?.filter(
        (location) =>
          !locationsMoving.some(
            (e) => e.lat === location.lat && e.lng === location.lng,
          ),
      );

      const concatenatedLocations =
        [...locationsMoving, ...(locationsTempt || [])]?.filter(
          (item) => !blacklistAddress?.includes(item?.address as string),
        ) || [];
      return concatenatedLocations;
    },
    [locations, user?.homeMovingLocations],
  );

  const onAddIsInBuilding = (onConfirm?: () => void) => {
    Alert.alert.open({
      title: t('TRANSPORTATION_IN_THE_SAME_BUILDING'),
      titleStyle: { fontSize: Spacing.SPACE_20 },
      message: (
        <ConfirmAddInBuildingTypeContent
          onConfirm={() => {
            onConfirm && onConfirm();
            const stairsTransportStep1 = oldHomeDetail?.options?.find(
              (e) => e.name === NameOptionByHomeTypeHomeMoving.stairsTransport,
            );
            const homeDetail: IHomeDetail = {
              addressDetail: oldHomeDetail?.addressDetail,
              homeType: oldHomeDetail?.homeType,
            };

            // Nếu nơi đi có option "Vận chuyển thang bộ"
            if (stairsTransportStep1) {
              // thì add option đó qua "Nơi đến"
              homeDetail.options = [stairsTransportStep1];

              // Set lại biến tạm để trường hợp khi người dùng bỏ chọn "Vận chuyển cùng tòa nhà"
              setTemptStairsTransportStep1(stairsTransportStep1);
              // clear option đó ở nơi đi
              setHomeDetail({
                step: HomeMovingProgressPostTaskType.Step1,
                homeDetail: {
                  ...oldHomeDetail,
                  options: oldHomeDetail?.options?.filter(
                    (e) =>
                      e.name !== NameOptionByHomeTypeHomeMoving.stairsTransport,
                  ),
                },
              });
            }

            // Set IsInBuilding = true
            setIsInBuilding(true);

            setHomeDetail({
              step: HomeMovingProgressPostTaskType.Step2,
              homeDetail,
            });
          }}
        />
      ),
    });
  };

  return {
    getIsShowAddIsInBuilding,
    getDetailSettingHomeMoving,
    getDetailSettingHomeType,
    getDataPostTaskMoving,
    getContentByStep,
    getBlackListLocation,
    getIconOptionHomeType,
    onChangeLocation,
    onChooseHomeType,
    onNextStep,
    getSubTitleOptionTypeFurniture,
    getSettingBookTask,
    getIsPriceIncrease,
    getAcreageText,
    checkIsSupportCity,
    getImageHomeType,
    getContentByHomeType,
    getContentOptionHomeType,
    getDateCleaningDefault,
    onPressStep,
    onAddLocation,
    getLocations,
    onAddIsInBuilding,
    getBookTaskAfterHours,
  };
};
