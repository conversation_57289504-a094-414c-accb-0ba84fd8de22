import React from 'react';
import {
  ColorsV2,
  CText,
  FontSizes,
  IconAssets,
  IconImage,
  SizedBox,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { styles } from './styles';

interface UploadImageButtonProps {
  label?: string;
  onPress?: () => void;
}

export const UploadImageButton = ({
  label,
  onPress,
}: UploadImageButtonProps) => {
  return (
    <TouchableOpacity
      activeOpacity={0.7}
      style={styles.container}
      onPress={onPress}
    >
      <IconImage
        source={IconAssets.icCamera}
        size={18}
        color={ColorsV2.neutral400}
      />
      <SizedBox width={Spacing.SPACE_08} />
      <CText
        color={ColorsV2.neutral400}
        center
        size={FontSizes.SIZE_14}
      >
        {label}
      </CText>
    </TouchableOpacity>
  );
};
